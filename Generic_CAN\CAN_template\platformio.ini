; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:teensy41]
platform = teensy
board = teensy41
framework = arduino
lib_deps = 
    pierremolinaro/ACAN_T4@^1.1.8

board_microros_distro = humble
board_microros_transport = native_ethernet

build_flags =
    -Iinclude
    -DTESTING_MODE
    -O2
    -DNDEBUG

check_tool = cppcheck, clangtidy
check_flags =
  --common-flag
  cppcheck: --enable=performance --inline-suppr
  clangtidy: -fix-errors -format-style=llvm
check_severity = low

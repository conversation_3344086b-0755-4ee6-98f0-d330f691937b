name=ACAN_T4
version=1.1.8
author=<PERSON>
maintainer=<PERSON> <<EMAIL>>
sentence=A Teensy 4.0 / 4.1 CAN / CANFD driver.
paragraph=This library is a CAN network driver for CAN1, CAN2 and CAN3. Compatible with ACAN2515, ACAN2517 libraries. It handles CANFD mode on CAN3, and is compatible with ACAN2517FD library. For CAN and CANFD, default configuration enables reception of all frames. Reception filters can be easily defined.
category=Communication
url=https://github.com/pierremolinaro/acan-t4
architectures=*

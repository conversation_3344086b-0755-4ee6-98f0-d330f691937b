//--------------------------------------------------------------------------------------------------
// A <PERSON>sy 4.x CAN driver
// by <PERSON>
// https://github.com/pierremolinaro/ACAN_T4
//
//--------------------------------------------------------------------------------------------------

#pragma once
#include <stdint.h>

//--------------------------------------------------------------------------------------------------

enum class ACAN_CAN_ROOT_CLOCK {
  CLOCK_24MHz,
  CLOCK_60MHz
//  CLOCK_80MHz // Not available (Silicon bug ERR050235, see RT1060_0N00X.pdf)
} ;

//--------------------------------------------------------------------------------------------------
//    SET CAN CLOCK
//--------------------------------------------------------------------------------------------------

bool setCANRootClock (const ACAN_CAN_ROOT_CLOCK inCANClock,
                      const uint32_t inCANClockDivisor) ;

//--------------------------------------------------------------------------------------------------
//    GET  CAN CLOCK
//--------------------------------------------------------------------------------------------------

ACAN_CAN_ROOT_CLOCK getCANRootClock (void) ;
uint32_t getCANRootClockFrequency (void) ; // 24 000 000, 60 000 000
uint32_t getCANRootClockDivisor (void) ; // 1 ... 64

//--------------------------------------------------------------------------------------------------

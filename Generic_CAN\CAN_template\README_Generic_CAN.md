# Generic CAN Template for Teensy

A flexible, serial-controlled CAN communication template for Teensy boards. Control any CAN device through simple serial commands.

## Features

✅ **Serial Command Interface** - Control CAN communication via Serial Monitor  
✅ **Single Baud Rate Macro** - Easy configuration with one setting  
✅ **Universal Protocol Support** - Works with any CAN protocol  
✅ **Real-time Monitoring** - See all CAN traffic in real-time  
✅ **Standard & Extended Frames** - Support for both 11-bit and 29-bit IDs  
✅ **RTR Frame Support** - Send Remote Transmission Requests  
✅ **Hex Data Parsing** - Easy hex string to byte conversion  

## Quick Setup

### 1. Hardware Connections
- **Teensy 4.0/4.1 CAN1 Pins:**
  - TX: Pin 22 (CRX1)
  - RX: Pin 23 (CTX1)
- Connect to CAN transceiver (MCP2551, TJA1050, etc.)
- Add 120Ω termination resistors at bus ends

### 2. Configure Baud Rate
Edit the macro in the code:
```cpp
#define CAN_BAUDRATE (250 * 1000)    // 250 kbps
```

Common baud rates:
- `(125 * 1000)` - 125 kbps
- `(250 * 1000)` - 250 kbps  
- `(500 * 1000)` - 500 kbps
- `(1000 * 1000)` - 1 Mbps

### 3. Upload and Connect
1. Upload the code to your Teensy
2. Open Serial Monitor at 115200 baud
3. Type `HELP` to see available commands

## Serial Commands

### SEND Command
Send any CAN message:
```
SEND <ID> <EXT> <RTR> <DATA>
```

**Parameters:**
- `ID`: CAN ID in hex (e.g., `123`, `18900140`)
- `EXT`: `0`=Standard(11-bit), `1`=Extended(29-bit)
- `RTR`: `0`=Data frame, `1`=RTR frame
- `DATA`: Hex bytes separated by spaces (optional for RTR)

**Examples:**
```
SEND 123 0 0 01 02 03 04        → Standard data frame
SEND 18DA00F1 1 0 AA BB CC      → Extended data frame
SEND 700 0 1                    → Standard RTR frame
SEND 18900140 1 1               → Extended RTR frame
```

### RTR Command (Shortcut)
Send RTR frames quickly:
```
RTR <ID> <EXT>
```

**Examples:**
```
RTR 18900140 1                  → Extended RTR frame
RTR 123 0                       → Standard RTR frame
```

### Other Commands
```
HELP     → Show command help
STATUS   → Show CAN bus status
CLEAR    → Clear screen
```

## Message Monitoring

### Received Messages
All incoming CAN messages are automatically displayed:
```
RX: ID=0x123, EXT=0, RTR=0, LEN=4, DATA=01 02 03 04
RX: ID=0x18904001, EXT=1, RTR=0, LEN=8, DATA=01 F4 E2 0F 75 30 03 E1
```

### Transmitted Messages
Confirmation of sent messages:
```
TX: ID=0x123, EXT=0, RTR=0, LEN=4, DATA=01 02 03 04 -> OK
TX: ID=0x18900140, EXT=1, RTR=1, LEN=8 -> OK
```

## Example Usage Scenarios

### 1. OBD-II Communication
```
# Request engine RPM
SEND 7DF 0 0 02 01 0C 00 00 00 00 00

# Expected response from ECU
RX: ID=0x7E8, EXT=0, RTR=0, LEN=8, DATA=04 41 0C 1A F8 00 00 00
```

### 2. CANopen Device Control
```
# Send heartbeat
SEND 701 0 0 05

# Request device status via SDO
SEND 601 0 0 40 00 10 01 00 00 00 00
```

### 3. J1939 Communication
```
# Request engine temperature
RTR 18FEEE00 1

# Send vehicle speed
SEND 18FEF100 1 0 00 00 FA 00 FF FF FF FF
```

### 4. Custom Protocol Testing
```
# Test your custom device
SEND 200 0 0 AA BB CC DD
SEND 300 0 0 11 22 33 44 55 66 77 88

# Send RTR to request data
RTR 400 0
```

## Programming Interface

### Direct Function Calls
You can also use the template functions directly in your code:

```cpp
// Send standard message
uint8_t data[] = {0x01, 0x02, 0x03, 0x04};
sendCANMessage(0x123, false, false, data, 4);

// Send extended RTR
sendExtendedRTR(0x18900140);

// Quick shortcuts
sendStandardMessage(0x123, data, 4);
sendExtendedMessage(0x18DA00F1, data, 3);
```

### Custom Message Processing
Add your own message handling in `processCANMessages()`:

```cpp
void processCANMessages() {
  while (ACAN_T4::can1.available()) {
    CANMessage message;
    ACAN_T4::can1.receive(message);
    
    // Your custom processing here
    if (message.id == 0x123) {
      // Handle specific message
      handleMyDevice(message.data);
    }
    
    // Standard monitoring output
    Serial.print("RX: ID=0x");
    Serial.print(message.id, HEX);
    // ... rest of output
  }
}
```

## Troubleshooting

### Common Issues

**1. CAN initialization failed**
```
CAN initialization FAILED. Error code: 0x...
```
- Check wiring connections
- Verify CAN transceiver power
- Ensure proper bus termination (120Ω)

**2. No messages received**
- Verify baud rate matches your CAN network
- Check CAN H/L connections
- Ensure other devices are on the bus

**3. Messages not sending**
```
TX: ... -> FAILED
```
- Bus may be off or in error state
- Check for bus conflicts
- Verify termination resistors

### Debug Tips

1. **Start Simple**: Test with known working CAN devices
2. **Check Baud Rate**: Most common issue is mismatched baud rates
3. **Use Oscilloscope**: Verify CAN signals on the bus
4. **Monitor Traffic**: Use the template to observe existing bus traffic
5. **Test Loopback**: Connect CAN H to CAN L for loopback testing

## Customization

### Adding Protocol-Specific Functions
```cpp
// Add to your code
void sendOBD2Request(uint8_t mode, uint8_t pid) {
  uint8_t data[8] = {0x02, mode, pid, 0, 0, 0, 0, 0};
  sendStandardMessage(0x7DF, data, 8);
}

void sendJ1939Message(uint32_t pgn, uint8_t* data, uint8_t len) {
  sendExtendedMessage(pgn, data, len);
}
```

### Multiple CAN Buses
To use CAN2 instead of CAN1, change:
```cpp
// Replace ACAN_T4::can1 with ACAN_T4::can2
ACAN_T4::can2.begin(canSettings);
ACAN_T4::can2.tryToSend(message);
ACAN_T4::can2.available();
ACAN_T4::can2.receive(message);
```

## Example Session

```
=== GENERIC CAN TEMPLATE ===
Initializing CAN bus at 250000 bps...
CAN bus initialized successfully!
Ready for commands. Type HELP for available commands.
> HELP
=== CAN COMMUNICATION COMMANDS ===
SEND <ID> <EXT> <RTR> <DATA> - Send CAN message
...
> SEND 123 0 0 01 02 03 04
TX: ID=0x123, EXT=0, RTR=0, LEN=4, DATA=01 02 03 04 -> OK
> RTR 18900140 1
TX: ID=0x18900140, EXT=1, RTR=1, LEN=8 -> OK
RX: ID=0x18904001, EXT=1, RTR=0, LEN=8, DATA=01 F4 E2 0F 75 30 03 E1
> STATUS
=== CAN BUS STATUS ===
Baud Rate: 250000 bps
Bus Status: Active
======================
>
```

This template provides a complete foundation for CAN communication testing and development. Simply change the baud rate macro and start communicating with any CAN device!

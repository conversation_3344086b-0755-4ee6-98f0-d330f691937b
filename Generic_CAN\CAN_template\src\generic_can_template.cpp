#include <Arduino.h>
#include <ACAN_T4.h>

// =============================================================================
// FUNCTION DECLARATIONS
// =============================================================================
void printHelp();
void printStatus();

// =============================================================================
// CONFIGURATION
// =============================================================================
#define CAN_BAUDRATE (1000 * 1000)    // Set your desired baud rate here (250 kbps default)
#define SERIAL_BAUDRATE 115200       // Serial communication baud rate
#define MAX_SERIAL_BUFFER 128        // Maximum serial input buffer size

// =============================================================================
// GLOBAL VARIABLES
// =============================================================================
String serialBuffer = "";
bool serialComplete = false;

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Convert hex string to integer
 * @param hexStr Hex string (e.g., "1A2B")
 * @return Converted integer value
 */
uint32_t hexStringToInt(String hexStr) {
  uint32_t result = 0;
  hexStr.toUpperCase();
  
  for (int i = 0; i < hexStr.length(); i++) {
    char c = hexStr.charAt(i);
    result = result * 16;
    
    if (c >= '0' && c <= '9') {
      result += c - '0';
    } else if (c >= 'A' && c <= 'F') {
      result += c - 'A' + 10;
    } else {
      Serial.println("ERROR: Invalid hex character");
      return 0;
    }
  }
  return result;
}

/**
 * Parse hex data string into byte array
 * @param dataStr Hex data string (e.g., "01 02 03 04")
 * @param dataArray Output byte array
 * @return Number of bytes parsed
 */
uint8_t parseHexData(String dataStr, uint8_t dataArray[8]) {
  uint8_t byteCount = 0;
  dataStr.trim();
  dataStr.replace(" ", "");  // Remove spaces
  
  // Parse pairs of hex characters
  for (int i = 0; i < dataStr.length() && byteCount < 8; i += 2) {
    if (i + 1 < dataStr.length()) {
      String byteStr = dataStr.substring(i, i + 2);
      dataArray[byteCount] = (uint8_t)hexStringToInt(byteStr);
      byteCount++;
    }
  }
  
  return byteCount;
}

/**
 * Print byte array as hex string
 * @param data Byte array
 * @param length Number of bytes
 */
void printHexData(const uint8_t data[], uint8_t length) {
  for (uint8_t i = 0; i < length; i++) {
    if (data[i] < 0x10) Serial.print("0");
    Serial.print(data[i], HEX);
    if (i < length - 1) Serial.print(" ");
  }
}

// =============================================================================
// CAN FUNCTIONS
// =============================================================================

/**
 * Send CAN message
 * @param id CAN ID
 * @param extended true for extended frame (29-bit), false for standard (11-bit)
 * @param rtr true for RTR frame, false for data frame
 * @param data Data bytes
 * @param length Data length (0-8)
 * @return true if sent successfully
 */
bool sendCANMessage(uint32_t id, bool extended, bool rtr, const uint8_t data[], uint8_t length) {
  CANMessage message;
  message.id = id;
  message.ext = extended;
  message.rtr = rtr;
  message.len = length;
  
  // For RTR frames, clear all data bytes (no data is sent in RTR)
  // For data frames, copy the provided data
  if (rtr) {
    // RTR frames don't carry data, clear all data bytes
    for (uint8_t i = 0; i < 8; i++) {
      message.data[i] = 0;
    }
  } else {
    // Copy data for regular data frames
    for (uint8_t i = 0; i < length && i < 8; i++) {
      message.data[i] = data[i];
    }
    // Clear unused bytes
    for (uint8_t i = length; i < 8; i++) {
      message.data[i] = 0;
    }
  }
  
  bool success = ACAN_T4::can1.tryToSend(message);
  
  // Print transmission status
  Serial.print("TX: ID=0x");
  Serial.print(id, HEX);
  Serial.print(", EXT=");
  Serial.print(extended ? "1" : "0");
  Serial.print(", RTR=");
  Serial.print(rtr ? "1" : "0");
  Serial.print(", LEN=");
  Serial.print(length);
  
  if (!rtr && length > 0) {
    Serial.print(", DATA=");
    printHexData(data, length);
  } else if (rtr) {
    Serial.print(" (Requesting ");
    Serial.print(length);
    Serial.print(" bytes)");
  }
  
  Serial.print(" -> ");
  Serial.println(success ? "OK" : "FAILED");
  
  return success;
}

/**
 * Process received CAN messages
 */
void processCANMessages() {
  while (ACAN_T4::can1.available()) {
    CANMessage message;
    ACAN_T4::can1.receive(message);
    
    // Print received message
    Serial.print("RX: ID=0x");
    Serial.print(message.id, HEX);
    Serial.print(", EXT=");
    Serial.print(message.ext ? "1" : "0");
    Serial.print(", RTR=");
    Serial.print(message.rtr ? "1" : "0");
    Serial.print(", LEN=");
    Serial.print(message.len);
    
    if (!message.rtr && message.len > 0) {
      Serial.print(", DATA=");
      printHexData(message.data, message.len);
    }
    
    Serial.println();
  }
}

// =============================================================================
// SERIAL COMMAND PROCESSING
// =============================================================================

/**
 * Process serial commands
 */
void processSerialCommand(String command) {
  command.trim();
  command.toUpperCase();
  
  if (command.startsWith("SEND ")) {
    // Parse SEND command
    // Format: SEND <ID> <EXT> <RTR> <DATA>
    // Example: SEND 123 0 0 01 02 03 04
    
    String params = command.substring(5);
    int spaceIndex1 = params.indexOf(' ');
    int spaceIndex2 = params.indexOf(' ', spaceIndex1 + 1);
    int spaceIndex3 = params.indexOf(' ', spaceIndex2 + 1);
    
    if (spaceIndex1 == -1 || spaceIndex2 == -1 || spaceIndex3 == -1) {
      Serial.println("ERROR: Invalid SEND format. Use: SEND <ID> <EXT> <RTR> <DATA>");
      Serial.println("Example: SEND 123 0 0 01 02 03 04");
      return;
    }
    
    String idStr = params.substring(0, spaceIndex1);
    String extStr = params.substring(spaceIndex1 + 1, spaceIndex2);
    String rtrStr = params.substring(spaceIndex2 + 1, spaceIndex3);
    String dataStr = params.substring(spaceIndex3 + 1);
    
    uint32_t id = hexStringToInt(idStr);
    bool extended = (extStr == "1");
    bool rtr = (rtrStr == "1");
    
    uint8_t data[8] = {0};
    uint8_t length = 0;
    
    if (!rtr && dataStr.length() > 0) {
      length = parseHexData(dataStr, data);
    }
    
    sendCANMessage(id, extended, rtr, data, length);
    
  } else if (command.startsWith("RTR ")) {
    // Parse RTR command (shortcut for RTR frames)
    // Format: RTR <ID> <EXT> <LENGTH>
    // Example: RTR 18900140 1 8

    String params = command.substring(4);
    int spaceIndex1 = params.indexOf(' ');
    int spaceIndex2 = params.indexOf(' ', spaceIndex1 + 1);

    if (spaceIndex1 == -1 || spaceIndex2 == -1) {
      Serial.println("ERROR: Invalid RTR format. Use: RTR <ID> <EXT> <LENGTH>");
      Serial.println("Example: RTR 18900140 1 8");
      Serial.println("LENGTH must be 2, 4, or 8 bytes");
      return;
    }

    String idStr = params.substring(0, spaceIndex1);
    String extStr = params.substring(spaceIndex1 + 1, spaceIndex2);
    String lengthStr = params.substring(spaceIndex2 + 1);

    uint32_t id = hexStringToInt(idStr);
    bool extended = (extStr == "1");
    uint8_t requestLength = lengthStr.toInt();

    // Validate request length (RTR frames typically request 2, 4, or 8 bytes)
    if (requestLength != 2 && requestLength != 4 && requestLength != 8) {
      Serial.println("ERROR: RTR length must be 2, 4, or 8 bytes");
      return;
    }

    uint8_t data[8] = {0}; // No data is sent in RTR frames
    sendCANMessage(id, extended, true, data, requestLength);
    
  } else if (command == "HELP") {
    printHelp();
    
  } else if (command == "STATUS") {
    printStatus();
    
  } else if (command == "CLEAR") {
    // Clear serial buffer
    Serial.println("Serial buffer cleared");
    
  } else {
    Serial.println("ERROR: Unknown command. Type HELP for available commands.");
  }
}

/**
 * Print help information
 */
void printHelp() {
  Serial.println("=== CAN COMMUNICATION COMMANDS ===");
  Serial.println("SEND <ID> <EXT> <RTR> <DATA> - Send CAN message");
  Serial.println("  ID: CAN ID in hex (e.g., 123 or 18900140)");
  Serial.println("  EXT: 0=Standard(11-bit), 1=Extended(29-bit)");
  Serial.println("  RTR: 0=Data frame, 1=RTR frame");
  Serial.println("  DATA: Hex bytes separated by spaces (e.g., 01 02 03)");
  Serial.println("");
  Serial.println("RTR <ID> <EXT> <LENGTH> - Send RTR frame (request data)");
  Serial.println("  ID: CAN ID in hex");
  Serial.println("  EXT: 0=Standard, 1=Extended");
  Serial.println("  LENGTH: Number of bytes to request (2, 4, or 8)");
  Serial.println("");
  Serial.println("STATUS - Show CAN bus status");
  Serial.println("HELP - Show this help");
  Serial.println("CLEAR - Clear screen");
  Serial.println("");
  Serial.println("Examples:");
  Serial.println("  SEND 123 0 0 01 02 03 04    (Standard data frame)");
  Serial.println("  SEND 18DA00F1 1 0 AA BB CC  (Extended data frame)");
  Serial.println("  RTR 18900140 1 8            (Extended RTR frame, request 8 bytes)");
  Serial.println("  RTR 123 0 4                 (Standard RTR frame, request 4 bytes)");
  Serial.println("  SEND 700 0 1                (Standard RTR frame via SEND command)");
  Serial.println("===================================");
}

/**
 * Print CAN bus status
 */
void printStatus() {
  Serial.println("=== CAN BUS STATUS ===");
  Serial.print("Baud Rate: ");
  Serial.print(CAN_BAUDRATE);
  Serial.println(" bps");
  Serial.print("Bus Status: ");
  // Note: ACAN_T4 doesn't provide direct bus status, so we'll show basic info
  Serial.println("Active");
  Serial.println("======================");
}

// =============================================================================
// SERIAL EVENT HANDLING
// =============================================================================

/**
 * Handle incoming serial data
 */
void serialEvent() {
  while (Serial.available()) {
    char inChar = (char)Serial.read();
    
    if (inChar == '\n' || inChar == '\r') {
      serialComplete = true;
    } else {
      if (serialBuffer.length() < MAX_SERIAL_BUFFER) {
        serialBuffer += inChar;
      }
    }
  }
}

// =============================================================================
// SETUP AND MAIN LOOP
// =============================================================================

void setup() {
  // Initialize serial communication
  Serial.begin(SERIAL_BAUDRATE);
  while (!Serial) {
    delay(10);
  }
  
  // Initialize built-in LED
  pinMode(LED_BUILTIN, OUTPUT);
  
  // Print startup message
  Serial.println("=== GENERIC CAN TEMPLATE ===");
  Serial.print("Initializing CAN bus at ");
  Serial.print(CAN_BAUDRATE);
  Serial.println(" bps...");
  
  // Configure CAN settings
  ACAN_T4_Settings canSettings(CAN_BAUDRATE);
  uint32_t errorCode = ACAN_T4::can1.begin(canSettings);
  
  if (errorCode == 0) {
    Serial.println("CAN bus initialized successfully!");
  } else {
    Serial.print("CAN initialization FAILED. Error code: 0x");
    Serial.println(errorCode, HEX);
    Serial.println("Check wiring and try again.");
    while (1) {
      digitalWrite(LED_BUILTIN, !digitalRead(LED_BUILTIN));
      delay(200);
    }
  }
  
  Serial.println("Ready for commands. Type HELP for available commands.");
  Serial.print("> ");
}

void loop() {
  // Blink LED to show activity
  digitalWrite(LED_BUILTIN, (millis() / 1000) % 2);
  
  // Process incoming CAN messages
  processCANMessages();
  
  // Handle serial events
  serialEvent();
  
  // Process complete serial commands
  if (serialComplete) {
    if (serialBuffer.length() > 0) {
      processSerialCommand(serialBuffer);
    }
    serialBuffer = "";
    serialComplete = false;
    Serial.print("> ");
  }
  
  // Small delay to prevent overwhelming the system
  delay(1);
}

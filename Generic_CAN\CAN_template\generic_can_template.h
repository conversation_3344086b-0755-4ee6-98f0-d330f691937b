#pragma once

#include <Arduino.h>
#include <ACAN_T4.h>

// =============================================================================
// CONFIGURATION MACROS
// =============================================================================
#define CAN_BAUDRATE (250 * 1000)    // Set your desired baud rate here
#define SERIAL_BAUDRATE 115200       // Serial communication baud rate
#define MAX_SERIAL_BUFFER 128        // Maximum serial input buffer size

// =============================================================================
// UTILITY FUNCTION DECLARATIONS
// =============================================================================

/**
 * Convert hex string to integer
 * @param hexStr Hex string (e.g., "1A2B")
 * @return Converted integer value
 */
uint32_t hexStringToInt(String hexStr);

/**
 * Parse hex data string into byte array
 * @param dataStr Hex data string (e.g., "01 02 03 04")
 * @param dataArray Output byte array
 * @return Number of bytes parsed
 */
uint8_t parseHexData(String dataStr, uint8_t dataArray[8]);

/**
 * Print byte array as hex string
 * @param data Byte array
 * @param length Number of bytes
 */
void printHexData(const uint8_t data[], uint8_t length);

// =============================================================================
// CAN FUNCTION DECLARATIONS
// =============================================================================

/**
 * Send CAN message
 * @param id CAN ID
 * @param extended true for extended frame (29-bit), false for standard (11-bit)
 * @param rtr true for RTR frame, false for data frame
 * @param data Data bytes
 * @param length Data length (0-8)
 * @return true if sent successfully
 */
bool sendCANMessage(uint32_t id, bool extended, bool rtr, const uint8_t data[], uint8_t length);

/**
 * Process received CAN messages
 */
void processCANMessages();

// =============================================================================
// SERIAL COMMAND FUNCTION DECLARATIONS
// =============================================================================

/**
 * Process serial commands
 * @param command Command string from serial input
 */
void processSerialCommand(String command);

/**
 * Print help information
 */
void printHelp();

/**
 * Print CAN bus status
 */
void printStatus();

/**
 * Handle incoming serial data
 */
void serialEvent();

// =============================================================================
// INLINE UTILITY FUNCTIONS
// =============================================================================

/**
 * Create a quick standard CAN message
 * @param id 11-bit CAN ID
 * @param data Data bytes
 * @param length Data length
 * @return true if sent successfully
 */
inline bool sendStandardMessage(uint16_t id, const uint8_t data[], uint8_t length) {
  return sendCANMessage(id, false, false, data, length);
}

/**
 * Create a quick extended CAN message
 * @param id 29-bit CAN ID
 * @param data Data bytes
 * @param length Data length
 * @return true if sent successfully
 */
inline bool sendExtendedMessage(uint32_t id, const uint8_t data[], uint8_t length) {
  return sendCANMessage(id, true, false, data, length);
}

/**
 * Send a standard RTR frame
 * @param id 11-bit CAN ID
 * @return true if sent successfully
 */
inline bool sendStandardRTR(uint16_t id) {
  uint8_t data[8] = {0};
  return sendCANMessage(id, false, true, data, 8);
}

/**
 * Send an extended RTR frame
 * @param id 29-bit CAN ID
 * @return true if sent successfully
 */
inline bool sendExtendedRTR(uint32_t id) {
  uint8_t data[8] = {0};
  return sendCANMessage(id, true, true, data, 8);
}

// =============================================================================
// COMMON CAN PROTOCOLS (EXAMPLES)
// =============================================================================

// CANopen Emergency Object
#define CANOPEN_EMERGENCY_BASE 0x080

// CANopen Heartbeat/Node Guard
#define CANOPEN_HEARTBEAT_BASE 0x700

// OBD-II Standard IDs
#define OBD2_REQUEST_ID 0x7DF
#define OBD2_RESPONSE_BASE 0x7E8

// J1939 Parameter Group Numbers (examples)
#define J1939_ENGINE_TEMP 0x00FEEE00
#define J1939_VEHICLE_SPEED 0x00FEF100

// =============================================================================
// BAUD RATE PRESETS
// =============================================================================
#define CAN_BAUD_125K  (125 * 1000)
#define CAN_BAUD_250K  (250 * 1000)
#define CAN_BAUD_500K  (500 * 1000)
#define CAN_BAUD_1M    (1000 * 1000)

// =============================================================================
// COMMAND FORMAT REFERENCE
// =============================================================================
/*
SERIAL COMMAND FORMATS:

1. SEND <ID> <EXT> <RTR> <DATA>
   - ID: CAN ID in hex (e.g., 123, 18900140)
   - EXT: 0=Standard(11-bit), 1=Extended(29-bit)
   - RTR: 0=Data frame, 1=RTR frame
   - DATA: Hex bytes separated by spaces (optional for RTR)
   
   Examples:
   SEND 123 0 0 01 02 03 04        (Standard data frame)
   SEND 18DA00F1 1 0 AA BB CC      (Extended data frame)
   SEND 700 0 1                    (Standard RTR frame)

2. RTR <ID> <EXT>
   - Shortcut for sending RTR frames
   - ID: CAN ID in hex
   - EXT: 0=Standard, 1=Extended
   
   Examples:
   RTR 18900140 1                  (Extended RTR frame)
   RTR 123 0                       (Standard RTR frame)

3. Other Commands:
   HELP     - Show command help
   STATUS   - Show CAN bus status
   CLEAR    - Clear screen

RECEIVED MESSAGE FORMAT:
RX: ID=0x123, EXT=0, RTR=0, LEN=4, DATA=01 02 03 04

TRANSMITTED MESSAGE FORMAT:
TX: ID=0x123, EXT=0, RTR=0, LEN=4, DATA=01 02 03 04 -> OK
*/
